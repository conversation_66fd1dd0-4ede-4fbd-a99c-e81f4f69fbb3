{"ast": null, "code": "// Configuration for the frontend application\n\n// Get API base URL from environment or use default\nexport const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';\n\n// WebSocket URL\nexport const WS_URL = `${API_BASE_URL.replace('http', 'ws')}/ws`;\n\n// Other configuration settings\nexport const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds\nexport const PRICE_REFRESH_INTERVAL = 5000; // 5 seconds", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "WS_URL", "replace", "DEFAULT_REFRESH_INTERVAL", "PRICE_REFRESH_INTERVAL"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/config.ts"], "sourcesContent": ["// Configuration for the frontend application\n\n// Get API base URL from environment or use default\nexport const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';\n\n// WebSocket URL\nexport const WS_URL = `${API_BASE_URL.replace('http', 'ws')}/ws`;\n\n// Other configuration settings\nexport const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds\nexport const PRICE_REFRESH_INTERVAL = 5000; // 5 seconds\n"], "mappings": "AAAA;;AAEA;AACA,OAAO,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAEpF;AACA,OAAO,MAAMC,MAAM,GAAG,GAAGJ,YAAY,CAACK,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK;;AAEhE;AACA,OAAO,MAAMC,wBAAwB,GAAG,KAAK,CAAC,CAAC;AAC/C,OAAO,MAAMC,sBAAsB,GAAG,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}