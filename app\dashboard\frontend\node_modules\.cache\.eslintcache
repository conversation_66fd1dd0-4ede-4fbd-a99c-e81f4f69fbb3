[{"C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\ThemeContext.tsx": "3", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\Login.tsx": "5", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLOptimization.tsx": "6", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\AutoTradeControl.tsx": "7", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ProtectedRoute.tsx": "8", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.js": "9", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\TradeDashboard.tsx": "10", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\DashboardLayout.tsx": "11", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\authService.ts": "12", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\tokenService.ts": "13", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\api.ts": "14", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\websocket.ts": "15", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\analyticsWebsocket.ts": "16", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\apiService.ts": "17", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\account\\AccountStatistics.tsx": "18", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\common\\TradesTable.tsx": "19", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\config.ts": "20", "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\utils\\formatters.ts": "21"}, {"size": 486, "mtime": *************, "results": "22", "hashOfConfig": "23"}, {"size": 1396, "mtime": *************, "results": "24", "hashOfConfig": "23"}, {"size": 3911, "mtime": *************, "results": "25", "hashOfConfig": "23"}, {"size": 5530, "mtime": *************, "results": "26", "hashOfConfig": "23"}, {"size": 3709, "mtime": *************, "results": "27", "hashOfConfig": "23"}, {"size": 739, "mtime": *************, "results": "28", "hashOfConfig": "23"}, {"size": 5941, "mtime": *************, "results": "29", "hashOfConfig": "23"}, {"size": 792, "mtime": *************, "results": "30", "hashOfConfig": "23"}, {"size": 10609, "mtime": 1750342947917, "results": "31", "hashOfConfig": "23"}, {"size": 12810, "mtime": 1750493316669, "results": "32", "hashOfConfig": "23"}, {"size": 4103, "mtime": 1750430998683, "results": "33", "hashOfConfig": "23"}, {"size": 2184, "mtime": 1750495092790, "results": "34", "hashOfConfig": "23"}, {"size": 3806, "mtime": 1750495138727, "results": "35", "hashOfConfig": "23"}, {"size": 6302, "mtime": 1750495049084, "results": "36", "hashOfConfig": "23"}, {"size": 8301, "mtime": 1750494588370, "results": "37", "hashOfConfig": "23"}, {"size": 4983, "mtime": 1750533039151, "results": "38", "hashOfConfig": "23"}, {"size": 2682, "mtime": 1750342948426, "results": "39", "hashOfConfig": "23"}, {"size": 1891, "mtime": 1750342948034, "results": "40", "hashOfConfig": "23"}, {"size": 3601, "mtime": 1750495016681, "results": "41", "hashOfConfig": "23"}, {"size": 420, "mtime": 1750533264314, "results": "42", "hashOfConfig": "23"}, {"size": 772, "mtime": 1750342948639, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8e78f1", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\contexts\\AuthContext.tsx", ["107"], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\MLOptimization.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\pages\\AutoTradeControl.tsx", [], ["108"], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\MLControls.js", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\TradeDashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\tokenService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\websocket.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\analyticsWebsocket.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\account\\AccountStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\components\\common\\TradesTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\Programming\\Crypto_App_V2\\app\\dashboard\\frontend\\src\\utils\\formatters.ts", [], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 2, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 2, "endColumn": 19}, {"ruleId": "113", "severity": 1, "message": "114", "line": 115, "column": 6, "nodeType": "115", "endLine": 115, "endColumn": 8, "suggestions": "116", "suppressions": "117"}, "@typescript-eslint/no-unused-vars", "'jwtDecode' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAutoTradingStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["118"], ["119"], {"desc": "120", "fix": "121"}, {"kind": "122", "justification": "123"}, "Update the dependencies array to be: [fetchAutoTradingStatus]", {"range": "124", "text": "125"}, "directive", "", [3685, 3687], "[fetchAutoTradingStatus]"]