// Configuration for the frontend application

// Get API base URL from environment or use default
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';

// WebSocket URL
export const WS_URL = `${API_BASE_URL.replace('http', 'ws')}/ws`;

// Other configuration settings
export const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds
export const PRICE_REFRESH_INTERVAL = 5000; // 5 seconds
