"""
Reinforcement learning environment for weight optimization.

This module contains a custom gym environment for training reinforcement
learning agents to optimize strategy weights.
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class WeightOptimizationEnv(gym.Env):
    """Custom gym environment for strategy weight optimization."""

    metadata = {'render.modes': ['human']}

    def __init__(self, market_data: pd.DataFrame, strategy_names: List[str],
                window_size: int = 10, max_steps: int = 100,
                reward_function: str = 'sharpe'):
        """Initialize the environment.

        Args:
            market_data: DataFrame with market data features
            strategy_names: List of strategy names
            window_size: Size of the observation window
            max_steps: Maximum number of steps per episode
            reward_function: Name of the reward function to use
        """
        super(WeightOptimizationEnv, self).__init__()

        self.logger = logging.getLogger(__name__)
        # --- Ensure all columns are numeric and cast to float32 ---
        if not all(np.issubdtype(dtype, np.number) for dtype in market_data.dtypes):
            raise ValueError("All columns in market_data must be numeric.")
        self.market_data = market_data.astype(np.float32)
        self.strategy_names = strategy_names
        self.window_size = window_size
        self.max_steps = max_steps
        self.reward_function = reward_function

        # Number of strategies
        self.num_strategies = len(strategy_names)

        # Number of features
        self.num_features = market_data.shape[1]

        # --- Add validation for sufficient data length ---
        min_required = self.window_size + self.max_steps + 2
        if len(self.market_data) < min_required:
            raise ValueError(
                f"Insufficient market_data rows ({len(self.market_data)}) for window_size={self.window_size} and max_steps={self.max_steps}. "
                f"At least {min_required} rows are required."
            )
        # --- End validation ---

        # Define action space (strategy weights)
        self.action_space = spaces.Box(
            low=0.0,
            high=1.0,
            shape=(self.num_strategies,),
            dtype=np.float32
        )

        # Define observation space (market data features)
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.window_size, self.num_features),
            dtype=np.float32
        )

        # Initialize state
        self.reset()

    def reset(self):
        """Reset the environment to initial state.

        Returns:
            Initial observation
        """
        # Reset current step
        self.current_step = 0

        # Reset portfolio value
        self.portfolio_value = 1.0

        # Reset portfolio history
        self.portfolio_history = [self.portfolio_value]

        # Reset weights history
        self.weights_history = []

        # Reset returns history
        self.returns_history = []

        # Choose a random starting point
        self.start_idx = np.random.randint(
            self.window_size,
            len(self.market_data) - self.max_steps - 1
        )

        # Get initial observation
        observation = self._get_observation()

        return observation

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """Take a step in the environment.

        Args:
            action: Strategy weights

        Returns:
            Tuple of (observation, reward, done, info)
        """
        # Convert raw action values to valid weights via softmax
        try:
            exp_vals = np.exp(action - np.max(action))
            weights = exp_vals / np.sum(exp_vals)
        except Exception as e:
            self.logger.warning(f"Action softmax failed ({e}); using uniform weights.")
            weights = np.ones(self.num_strategies, dtype=np.float32) / self.num_strategies

        # Store weights
        self.weights_history.append(weights)

        # Get current market data
        current_idx = self.start_idx + self.current_step
        current_returns = self.market_data.iloc[current_idx]['returns']

        # Calculate strategy returns (simplified)
        # In a real system, this would use actual strategy performance
        strategy_returns = np.random.normal(
            loc=current_returns,
            scale=0.01,
            size=self.num_strategies
        )
        if np.any(np.isnan(strategy_returns)) or np.any(np.isinf(strategy_returns)):
            self.logger.warning(f"NaN/inf detected in strategy_returns: {strategy_returns}, setting to zeros.")
            strategy_returns = np.zeros(self.num_strategies)

        # Calculate portfolio return
        portfolio_return = np.sum(weights * strategy_returns)
        if np.isnan(portfolio_return) or np.isinf(portfolio_return):
            self.logger.warning(f"NaN/inf detected in portfolio_return: {portfolio_return}, setting to 0.0.")
            portfolio_return = 0.0

        # Store return
        self.returns_history.append(portfolio_return)

        # Update portfolio value
        self.portfolio_value *= (1 + portfolio_return)
        if np.isnan(self.portfolio_value) or np.isinf(self.portfolio_value):
            self.logger.warning(f"NaN/inf detected in portfolio_value: {self.portfolio_value}, resetting to 1.0.")
            self.portfolio_value = 1.0
        self.portfolio_history.append(self.portfolio_value)

        # Increment step
        self.current_step += 1

        # Check if done
        done = self.current_step >= self.max_steps

        # Get next observation
        observation = self._get_observation()

        # Calculate reward
        reward = self._calculate_reward()
        if np.isnan(reward) or np.isinf(reward):
            self.logger.warning(f"NaN/inf detected in reward: {reward}, setting to 0.0.")
            reward = 0.0

        # Create info dict
        info = {
            'portfolio_value': self.portfolio_value,
            'portfolio_return': portfolio_return,
            'weights': weights,
            'step': self.current_step
        }

        return observation, reward, done, info

    def render(self, mode='human'):
        """Render the environment.

        Args:
            mode: Rendering mode
        """
        if mode == 'human':
            # Print current state
            self.logger.info(f"Step: {self.current_step}")
            self.logger.info(f"Portfolio Value: {self.portfolio_value:.4f}")

            if self.weights_history:
                weights = self.weights_history[-1]
                weights_str = ", ".join([f"{self.strategy_names[i]}: {w:.4f}" for i, w in enumerate(weights)])
                self.logger.info(f"Weights: {weights_str}")

            if self.returns_history:
                self.logger.info(f"Last Return: {self.returns_history[-1]:.4f}")

    def _get_observation(self) -> np.ndarray:
        """Get the current observation.

        Returns:
            Current observation
        """
        # Get market data for the current window
        start = self.start_idx + self.current_step - self.window_size
        end = self.start_idx + self.current_step

        # Ensure start is not negative
        start = max(0, start)

        # Get the data
        data = self.market_data.iloc[start:end].values.astype(np.float32)

        # Pad if needed
        if len(data) < self.window_size:
            padding = np.zeros((self.window_size - len(data), self.num_features), dtype=np.float32)
            data = np.vstack([padding, data])

        # --- Explicit NaN/inf check for observation ---
        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
            self.logger.warning(f"NaN/inf detected in observation: {data}, replacing with zeros.")
            data = np.zeros((self.window_size, self.num_features), dtype=np.float32)

        return data

    def _calculate_reward(self) -> float:
        """Calculate the reward.

        Returns:
            Reward value
        """
        if len(self.returns_history) < 2:
            return 0.0

        if self.reward_function == 'sharpe':
            # Calculate Sharpe ratio
            returns = np.array(self.returns_history)
            mean_return = np.mean(returns)
            std_return = np.std(returns)

            # Avoid division by zero
            if std_return == 0:
                return 0.0

            sharpe = mean_return / std_return
            return sharpe

        elif self.reward_function == 'return':
            # Use the latest return
            return self.returns_history[-1]

        elif self.reward_function == 'cumulative':
            # Use the cumulative return
            # The portfolio_value is the current value of the portfolio
            # A value of 1.05 means a 5% return
            return self.portfolio_value - 1.0

        else:
            self.logger.warning(f"Unknown reward function: {self.reward_function}")
            return 0.0
