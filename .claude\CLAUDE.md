# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Python/FastAPI)
```bash
# Start main trading engine
python main.py

# Start dashboard API server
python -m app.dashboard.main

# Run tests
pytest

# Run tests with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/test_file.py
```

### Frontend (React/TypeScript)
```bash
# Navigate to frontend directory
cd app/dashboard/frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run frontend tests
npm test
```

### Full Application Startup
```bash
# Terminal 1: Start backend API
python -m app.dashboard.main

# Terminal 2: Start frontend (from app/dashboard/frontend)
npm start

# Access dashboard at http://localhost:3000
```

## High-Level Architecture

### Core Application Structure
The system uses a **microservices-style architecture** with two main entry points:
- **Main Trading Engine** (`main.py`) - Core trading logic and strategy execution
- **Dashboard API** (`app/dashboard/main.py`) - Web interface and monitoring

### Key Service Layers

#### Exchange Integration Layer
- `app/services/exchange/binance_client.py` - Primary Binance API interface
- `app/services/exchange/exchange_client.py` - Abstract exchange interface
- Handles market data, order placement, account management

#### Execution Service Layer
- `app/services/execution/execution_service.py` - Main trade execution orchestrator
- Modular design with mixins for lifecycle, order placement, trade operations
- `app/services/execution/order_manager.py` - Order lifecycle management
- `app/services/execution/trade_management.py` - Position and trade tracking

#### Strategy Layer
- `app/strategies/strategy_selector.py` - Main strategy orchestration engine
- Three core strategies:
  - **GridStrategy** - Range-bound market trading
  - **TechnicalAnalysisStrategy** - RSI/MACD/Bollinger Bands trading
  - **TrendFollowingStrategy** - EMA crossover and momentum trading

#### ML/AI Layer
- `app/ml/models/weight_optimizer.py` - Reinforcement learning for strategy optimization
- Uses PPO (Proximal Policy Optimization) for strategy weight learning
- `app/ml/data_collector.py` - Training data collection and preparation
- `app/ml/feature_engineering.py` - Market feature extraction

### Data Flow Architecture
```
Market Data → Exchange Client → Strategy Selector → Strategy Selection → Execution Service → Orders
     ↓                                    ↓
ML Feature Engineering ←→ ML Weight Optimizer → Strategy Weights
     ↓                                    ↓
WebSocket Updates → Dashboard API → Frontend UI
```

### Strategy Selection Process
1. **Market Analysis**: Calculate volatility, trend strength, range-bound conditions, volume
2. **Strategy Scoring**: Each strategy scores its suitability (0-1) for current market conditions
3. **ML Enhancement**: Optional reinforcement learning model provides learned strategy weights
4. **Strategy Selection**: Best strategy selected based on scores/ML weights
5. **Trade Execution**: Selected strategy generates signals and executes trades

### Frontend Architecture
- **React/TypeScript** with Material-UI components
- **Authentication**: JWT-based with AuthContext
- **Real-time Updates**: WebSocket integration for live data
- **Main Components**:
  - `TradeDashboard.tsx` - Real-time monitoring and manual controls
  - `MLOptimization.tsx` - Model training and evaluation interface
  - `AccountStatistics.tsx` - Performance metrics and account status

## Development Guidelines

### Project Structure Conventions
- **Services**: Business logic and external integrations in `app/services/`
- **Models**: Data models and schemas in `app/models/`
- **Strategies**: Trading strategy implementations in `app/strategies/`
- **ML Components**: Machine learning models and training in `app/ml/`
- **API Routes**: Dashboard API endpoints in `app/dashboard/`
- **Frontend**: React components in `app/dashboard/frontend/src/`

### Testing Strategy
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test service interactions
- **ML Tests**: Test machine learning model training and evaluation
- All tests in `tests/` directory, mirroring the `app/` structure

### Configuration Management
- Environment variables defined in `.env` file
- Settings centralized in `app/config/settings.py`
- Machine learning parameters in `app/config/app_config.py`

### Key Development Patterns
- **Dependency Injection**: Services injected via FastAPI dependency system
- **Strategy Pattern**: Pluggable trading strategies with common interface
- **Observer Pattern**: WebSocket handlers for real-time updates
- **ML Integration**: Optional reinforcement learning enhancement for strategy selection

### Important Implementation Notes
- **Exchange Client**: Abstracted to support multiple exchanges (currently Binance)
- **Strategy Switching**: Includes cooldown periods and position closure
- **Risk Management**: Built-in position sizing, stop-loss, and take-profit mechanisms
- **ML Model Persistence**: Models saved to `models/` directory with joblib
- **WebSocket Handling**: Real-time market data and order updates via WebSocket connections

## Claude AI Assistant Development Guidelines

### Core Development Rules

#### 1. Virtual Environment Management
**CRITICAL REQUIREMENT**: Always activate the virtual environment before running any command.

**For WSL/Linux environments:**
```bash
# Always prefix Python commands with venv activation
source venv/bin/activate && python script.py
source venv/bin/activate && pip install package
source venv/bin/activate && pytest tests/
```

**For Windows Command Prompt/PowerShell:**
```bash
# Use Windows venv activation
venv\Scripts\activate && python script.py
venv\Scripts\activate && pip install package
```

**Platform Detection:**
```bash
# Auto-detect platform and use appropriate activation
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
elif [ -f "venv/Scripts/activate" ]; then
    source venv/Scripts/activate
fi
```

#### 2. Directory Context Management
**CRITICAL REQUIREMENT**: Always make sure you are in the right directory before running directory commands.

```bash
# Verify current location first
pwd
ls

# Navigate to project root if needed
cd /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2

# Then run commands
source venv/bin/activate && python script.py
```

#### 3. Path Management Protocol
**CRITICAL REQUIREMENT**: Always run commands from the project directory, then using absolute paths.

```bash
# Project root directory
PROJECT_ROOT="/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2"

# Preferred approach - from project directory
cd $PROJECT_ROOT
source venv/bin/activate && python app/dashboard/main.py

# Alternative - using absolute paths
source $PROJECT_ROOT/venv/bin/activate && python $PROJECT_ROOT/app/dashboard/main.py
```

#### 4. Sub-Agent Task Management
**CRITICAL REQUIREMENT**: Use sub agents when necessary for productivity efficiency for mutually exclusive tasks.

##### When to Use Task Tool (Sub-Agents):
- **File Search Operations**: When searching for keywords, patterns, or files across large codebases
- **Parallel Analysis**: When analyzing multiple independent components simultaneously
- **Research Tasks**: When gathering information that doesn't interfere with current work
- **Independent Validation**: When testing separate systems or components

##### Examples:
```bash
# Deploy sub-agent for file search while working on fixes
Task: "Search for all occurrences of 'redis' configuration in the codebase"

# Use sub-agent for parallel testing
Task: "Test all MCP server connections and report status"

# Deploy for independent research
Task: "Analyze trading strategy performance metrics in logs"
```

##### Task Tool Usage Guidelines:
- Use for **mutually exclusive** operations that won't interfere with main task
- Provide **detailed, specific instructions** to sub-agents
- Specify **exactly what information** to return
- Use when task will take **multiple search/read operations**

### Command Execution Standards

#### Error Prevention Protocol
1. **Check current directory**: `pwd`
2. **Verify files exist**: `ls` or `find`
3. **Activate venv**: `source venv/bin/activate`
4. **Execute command**: Run the actual command

#### Timeout Management
- **Standard operations**: 30-60 seconds
- **Build processes**: 120-300 seconds  
- **ML training**: 300+ seconds or no timeout
- **Integration tests**: 120+ seconds

```bash
# Examples with appropriate timeouts
source venv/bin/activate && timeout 60s python test_script.py
source venv/bin/activate && timeout 300s python train_model.py
```

### MCP Server Management

#### Available MCP Servers
- **Supabase**: Database operations
- **GitHub**: Repository management
- **Playwright**: Web automation and testing
- **Redis**: General caching
- **Redis-Trading**: Trading-specific caching
- **Time-MCP**: Timestamp utilities
- **CoinCap**: Cryptocurrency data
- **WANDB**: ML experiment tracking
- **Jupyter**: Interactive development

#### MCP Troubleshooting
```bash
# Check MCP status
claude mcp list

# Get help for MCP issues
claude mcp help

# Restart MCP connections if needed
claude mcp restart
```

### Quality Assurance Rules

#### Before Code Changes
1. **Read existing files** to understand current patterns
2. **Follow existing code style** and conventions
3. **Test changes** before marking tasks complete
4. **Verify dependencies** are available in the codebase

#### After Code Changes
1. **Run linting**: Check for style issues
2. **Run tests**: Ensure functionality works
3. **Verify builds**: Confirm no compilation errors
4. **Update documentation**: If interfaces changed

---

**Guidelines Last Updated**: 2025-06-21T09:31:27.555Z (via Time MCP)