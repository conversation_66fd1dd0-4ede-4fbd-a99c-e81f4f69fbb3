{"env": {"BASH_DEFAULT_TIMEOUT_MS": "900000", "BASH_MAX_TIMEOUT_MS": "900000"}, "permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(npm uninstall:*)", "Bash(npm test:*)", "Bash(pip install:*)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python3 -c \"from app import database; print(''Import successful'')\")", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(source:*)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -c \"from tests.test_database_initialization import test_database_and_models_can_be_imported; test_database_and_models_can_be_imported(); print(''Basic import test passed'')\")", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -c \"from tests.test_database_initialization import test_database_and_models_can_be_imported; test_database_and_models_can_be_imported(); print(''Basic import test passed'')\")", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py::test_core_module_imports -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py::test_configuration_models -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py::test_type_definitions -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py::test_strategy_imports -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_settings_validation.py -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_trade_validation.py -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_settings_validation.py::test_jwt_configuration_weak_key -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_trade_validation.py::test_trade_validation_missing_fields -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_trade_validator_comprehensive.py -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_trade_validator_comprehensive.py::TestSideValidation::test_invalid_sides -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -c \"\nfrom app.utils.trade_validator import TradeValidator, TradeValidationError\ninvalid_sides = [''buy'', ''sell'', ''LONG'', ''SHORT'', 123, None]\nfor side in invalid_sides:\n    try:\n        TradeValidator.validate_side(side)\n        print(f''FAILED: {side} should have raised error'')\n    except TradeValidationError as e:\n        print(f''PASS: {side} raised: {e}'')\n    except Exception as e:\n        print(f''UNEXPECTED: {side} raised: {e}'')\n\")", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_basic_imports.py tests/test_settings_validation.py tests/test_trade_validation.py tests/test_trade_validator_comprehensive.py -v --tb=short)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/test_settings_validation.py::test_settings_with_missing_env_variables -v)", "Bash(PYTHONPATH=\"/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2\" python -m pytest tests/ --co -q)", "Bash(pip3 install:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(timeout 10s npx -y @modelcontextprotocol/server-postgres --help)", "Bash(timeout 10s npx -y @supabase/mcp-server-supabase@latest --access-token ******************************************** --help)", "Bash(npx:*)", "mcp__supabase__list_organizations", "mcp__github__search_repositories", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_install", "Bash(timeout 30s npx -y @modelcontextprotocol/server-coincap --help)", "Bash(timeout 30s npx -y @docker/mcp --help)", "Bash(timeout 30s npx -y @datalayer/jupyter-mcp-server --help)", "<PERSON><PERSON>(timeout 30s npx -y mlflowMCPServer --help)", "Bash(timeout 30s npx -y @redis/mcp-server --help)", "Bash(timeout 30s npx -y @qpd-v/mcp-communicator-telegram --help)", "Bash(timeout 30s npx -y @wandb/wandb-mcp-server --help)", "Bash(timeout 30s npx -y @zenml-io/mcp-zenml --help)", "Bash(npm search mcp)", "Bash(npm search:*)", "<PERSON><PERSON>(mcp:*)", "Bash(timeout 15s npm view mcp-crypto-price --json)", "Bash(timeout 15s npm view docker-mcp --json)", "Bash(timeout 15s npm view jupyter-mcp-server --json)", "Ba<PERSON>(timeout 15s npm view mlflowMCPServer --json)", "Bash(timeout 15s npm view @redis/mcp-redis --json)", "<PERSON><PERSON>(timeout 15s npm view mcp-telegram --json)", "Bash(timeout 15s npm view wandb-mcp-server --json)", "Bash(timeout 15s npm view mcp-zenml --json)", "Bash(timeout 15s npm view @gongrzhe/server-redis-mcp --json)", "Bash(timeout 15s npm view redis-mcp --json)", "<PERSON><PERSON>(timeout:*)", "Bash(npm run build:*)", "Bash(redis-cli:*)", "Bash(node:*)", "Bash(REDIS_URL=redis://localhost:6379 REDIS_DB=0 timeout 5s node dist/index.js)", "<PERSON><PERSON>(claude --debug)", "Bash(git add:*)", "Bash(git rm:*)", "Bash(npm install)", "mcp__redis-trading__get_cache_stats", "mcp__supabase__list_projects", "mcp__mcp-telegram__search_dialogs", "mcp__Time-MCP__get_current_time", "mcp__jupyter__read_notebook_source_only", "Bash(uvx:*)", "<PERSON>sh(redis-server:*)", "mcp__redis-trading__cache_strategy_weights", "mcp__redis-trading__cache_aggregated_signals", "mcp__redis-trading__cache_portfolio_metrics", "mcp__redis-trading__cache_kelly_stats", "mcp__redis-trading__cache_volatility_adjustment", "mcp__redis-trading__get_strategy_weights", "mcp__redis-trading__get_aggregated_signals", "mcp__redis-trading__get_portfolio_metrics", "mcp__redis-trading__get_kelly_stats", "mcp__jupyter__read_notebook_with_outputs", "mcp__jupyter__execute_cell", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(mkdir:*)", "mcp__redis__get", "Bash(REDIS_URL=redis://localhost:6379 node mcp-redis-trading/dist/index.js 2 >& 1)", "mcp__redis__list", "mcp__playwright__browser_close", "mcp__jupyter__add_cell", "mcp__redis__delete", "mcp__supabase__list_tables", "mcp__redis__set", "Bash(WANDB_API_KEY=**************************************** timeout 10 uvx --from git+https://github.com/wandb/wandb-mcp-server wandb_mcp_server 2 >& 1)", "Bash(npm run test:*)", "Bash(REDIS_URL=redis://localhost:6379 timeout 10 node /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/mcp-redis-trading/dist/index.js)", "Bash(npm ls:*)", "Bash(REDIS_URL=redis://localhost:6379 timeout 5s node dist/index.js)", "mcp__wandb__query_wandb_entity_projects", "mcp__coincap__get-crypto-price", "mcp__zenml__get_active_user", "mcp__ide__getDiagnostics", "mcp__wandb__query_wandb_support_bot", "Bash(powershell.exe -ExecutionPolicy Bypass -File /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/mcp-telegram-wrapper.ps1 test)", "mcp__ide__executeCode", "Bash(zenml:*)", "<PERSON><PERSON>(chmod:*)", "mcp__supabase__get_project", "mcp__supabase__apply_migration", "mcp__supabase__execute_sql", "Bash(export SUPABASE_URL=https://cxajtfyiilhauqbqifxc.supabase.co)", "Bash(export:*)", "Bash(export REDIS_URL=redis://localhost:6379)", "Bash(export WANDB_API_KEY=test_key)", "Bash(git commit:*)", "Bash(git push:*)", "mcp__mcp-playwright__playwright_navigate", "<PERSON><PERSON>(curl:*)", "mcp__mcp-playwright__playwright_screenshot", "mcp__mcp-playwright__playwright_get_visible_text", "mcp__mcp-playwright__playwright_close", "Bash(pwsh:*)", "Bash(powershell.exe:*)", "Bash(ss:*)", "mcp__mcp-playwright__playwright_get_visible_html", "mcp__mcp-playwright__playwright_fill", "mcp__mcp-playwright__playwright_click", "mcp__mcp-playwright__playwright_evaluate", "mcp__mcp-playwright__playwright_console_logs"], "deny": []}}