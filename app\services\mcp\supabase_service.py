"""
Supabase Service for Real-time Portfolio Analytics
Provides real-time data storage and analytics triggers for the ensemble system.
"""

from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import logging
import asyncio
import json
import os
from dataclasses import asdict

# Mock Supabase client for development (replace with actual when configured)
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    logging.warning("Supabase client not available. Using mock implementation.")

logger = logging.getLogger(__name__)


class MockSupabaseClient:
    """Mock Supabase client for testing without credentials."""
    
    def __init__(self):
        self.data_store = {}
        self.subscribers = {}
    
    def table(self, table_name: str):
        return MockTable(table_name, self.data_store)
    
    def channel(self, channel_name: str):
        return MockChannel(channel_name, self.subscribers)


class MockTable:
    """Mock table operations."""
    
    def __init__(self, table_name: str, data_store: Dict):
        self.table_name = table_name
        self.data_store = data_store
        
        if table_name not in self.data_store:
            self.data_store[table_name] = []
    
    def insert(self, data: Dict):
        data['id'] = len(self.data_store[self.table_name]) + 1
        data['created_at'] = datetime.now().isoformat()
        self.data_store[self.table_name].append(data)
        return MockQuery(self.table_name, self.data_store, insert_data=data)
    
    def select(self, columns: str = "*"):
        return MockQuery(self.table_name, self.data_store, columns)
    
    def update(self, data: Dict):
        return MockQuery(self.table_name, self.data_store, update_data=data)
    
    def delete(self):
        return MockQuery(self.table_name, self.data_store, delete=True)


class MockQuery:
    """Mock query operations."""
    
    def __init__(self, table_name: str, data_store: Dict, columns: str = "*", update_data: Dict = None, delete: bool = False, insert_data: Dict = None):
        self.table_name = table_name
        self.data_store = data_store
        self.columns = columns
        self.update_data = update_data
        self.delete = delete
        self.insert_data = insert_data
        self.filters = []
    
    def eq(self, column: str, value: Any):
        self.filters.append(('eq', column, value))
        return self
    
    def gt(self, column: str, value: Any):
        self.filters.append(('gt', column, value))
        return self
    
    def lt(self, column: str, value: Any):
        self.filters.append(('lt', column, value))
        return self
    
    def order(self, column: str, desc: bool = False):
        return self
    
    def limit(self, count: int):
        return self
    
    def execute(self):
        # Handle insert operation
        if self.insert_data:
            return MockResponse([self.insert_data])
        
        data = self.data_store.get(self.table_name, [])
        
        # Apply filters
        filtered_data = data
        for filter_type, column, value in self.filters:
            if filter_type == 'eq':
                filtered_data = [row for row in filtered_data if row.get(column) == value]
        
        return MockResponse(filtered_data)


class MockResponse:
    """Mock response object."""
    
    def __init__(self, data: List[Dict]):
        self.data = data


class MockChannel:
    """Mock real-time channel."""
    
    def __init__(self, channel_name: str, subscribers: Dict):
        self.channel_name = channel_name
        self.subscribers = subscribers
    
    def on(self, event: str, callback: Callable):
        if self.channel_name not in self.subscribers:
            self.subscribers[self.channel_name] = {}
        self.subscribers[self.channel_name][event] = callback
        return self


class SupabaseService:
    """Supabase service for real-time portfolio analytics."""
    
    def __init__(self, url: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize Supabase service.
        
        Args:
            url: Supabase project URL
            key: Supabase API key
        """
        self.url = url or os.getenv('SUPABASE_URL')
        self.key = key or os.getenv('SUPABASE_KEY')
        
        if SUPABASE_AVAILABLE and self.url and self.key:
            try:
                self.supabase: Client = create_client(self.url, self.key)
                # Test the connection with a simple query
                test_result = self.supabase.table('portfolio_metrics').select('*').limit(1).execute()
                logger.info(f"Connected to real Supabase: {self.url}")
                self.using_real_supabase = True
            except Exception as e:
                logger.warning(f"Failed to connect to real Supabase: {e}")
                logger.info("Falling back to mock Supabase client")
                self.supabase = MockSupabaseClient()
                self.using_real_supabase = False
        else:
            self.supabase = MockSupabaseClient()
            self.using_real_supabase = False
            if not SUPABASE_AVAILABLE:
                logger.info("Supabase client not available. Using mock implementation.")
            else:
                logger.info("Supabase credentials not configured. Using mock implementation.")
        
        self.real_time_subscribers = {}
    
    async def execute_query(self, query: str, *params) -> List[Dict[str, Any]]:
        """Execute a raw SQL query and return results."""
        try:
            if self.using_real_supabase:
                # For real Supabase, we'll use standard table operations instead of raw SQL
                # since execute_sql function doesn't exist by default
                logger.warning(f"Raw SQL not supported in Supabase, skipping query: {query[:50]}...")
                return []
            else:
                # Mock implementation - return empty result
                logger.warning(f"Mock Supabase cannot execute query: {query[:50]}...")
                return []
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []
    
    # Portfolio Metrics Storage
    
    async def store_portfolio_metrics(self, metrics: Dict[str, Any]) -> Optional[str]:
        """
        Store portfolio metrics with real-time updates.
        
        Args:
            metrics: Portfolio metrics dictionary
            
        Returns:
            Record ID if successful, None otherwise
        """
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'total_pnl': float(metrics.get('total_pnl', 0)),
                'sharpe_ratio': float(metrics.get('sharpe_ratio', 0)),
                'max_drawdown': float(metrics.get('max_drawdown', 0)),
                'win_rate': float(metrics.get('win_rate', 0)),
                'strategy_contributions': json.dumps(metrics.get('strategy_contributions', {})),
                'correlation_matrix': json.dumps(metrics.get('correlation_matrix', {}))
            }
            
            result = self.supabase.table('portfolio_metrics').insert(data).execute()
            
            if result.data:
                record_id = result.data[0].get('id')
                logger.info(f"Stored portfolio metrics with ID: {record_id}")
                return str(record_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to store portfolio metrics: {e}")
            return None
    
    async def store_trade_execution(self, trade_data: Dict[str, Any]) -> Optional[str]:
        """
        Store trade execution with strategy attribution.
        
        Args:
            trade_data: Trade execution data
            
        Returns:
            Record ID if successful, None otherwise
        """
        try:
            data = {
                'strategy_name': trade_data.get('strategy_name'),
                'symbol': trade_data.get('symbol'),
                'action': trade_data.get('action'),
                'quantity': float(trade_data.get('quantity', 0)),
                'price': float(trade_data.get('price', 0)),
                'executed_at': trade_data.get('timestamp', datetime.now().isoformat()),
                'pnl': float(trade_data.get('pnl', 0)),
                'return_pct': float(trade_data.get('return_pct', 0)),
                'fees': float(trade_data.get('fees', 0)),
                'confidence': float(trade_data.get('confidence', 0)),
                'weight_used': float(trade_data.get('weight', 0)),
                'position_size': float(trade_data.get('position_size', 0)),
                'market_conditions': json.dumps(trade_data.get('market_conditions', {}))
            }
            
            result = self.supabase.table('trades').insert(data).execute()
            
            if result.data:
                record_id = result.data[0].get('id')
                logger.info(f"Stored trade execution with ID: {record_id}")
                return str(record_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to store trade execution: {e}")
            return None
    
    async def store_strategy_weights(self, weights: Dict[str, float], metadata: Optional[Dict] = None) -> Optional[str]:
        """
        Store strategy weight history.
        
        Args:
            weights: Strategy weights dictionary
            metadata: Additional metadata
            
        Returns:
            Record ID if successful, None otherwise
        """
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'grid_weight': float(weights.get('GridStrategy', 0)),
                'technical_analysis_weight': float(weights.get('TechnicalAnalysisStrategy', 0)),
                'trend_following_weight': float(weights.get('TrendFollowingStrategy', 0)),
                'model_confidence': float(metadata.get('confidence', 0) if metadata else 0),
                'market_regime': metadata.get('market_regime', 'unknown') if metadata else 'unknown'
            }
            
            result = self.supabase.table('strategy_weights').insert(data).execute()
            
            if result.data:
                record_id = result.data[0].get('id')
                logger.debug(f"Stored strategy weights with ID: {record_id}")
                return str(record_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to store strategy weights: {e}")
            return None
    
    async def store_strategy_performance(self, strategy_name: str, performance_data: Dict[str, Any]) -> Optional[str]:
        """
        Store individual strategy performance.
        
        Args:
            strategy_name: Name of the strategy
            performance_data: Performance metrics
            
        Returns:
            Record ID if successful, None otherwise
        """
        try:
            data = {
                'strategy_name': strategy_name,
                'timestamp': datetime.now().isoformat(),
                'pnl': float(performance_data.get('pnl', 0)),
                'return_pct': float(performance_data.get('return_pct', 0)),
                'trades_count': int(performance_data.get('trades_count', 0)),
                'win_rate': float(performance_data.get('win_rate', 0)),
                'confidence_score': float(performance_data.get('confidence_score', 0))
            }
            
            result = self.supabase.table('strategy_performance').insert(data).execute()
            
            if result.data:
                record_id = result.data[0].get('id')
                logger.debug(f"Stored strategy performance for {strategy_name} with ID: {record_id}")
                return str(record_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to store strategy performance: {e}")
            return None
    
    async def store_alert(self, alert_type: str, severity: str, message: str, metrics_snapshot: Optional[Dict] = None) -> Optional[str]:
        """
        Store alert in the alerts table.
        
        Args:
            alert_type: Type of alert
            severity: Alert severity level
            message: Alert message
            metrics_snapshot: Snapshot of metrics when alert triggered
            
        Returns:
            Record ID if successful, None otherwise
        """
        try:
            data = {
                'alert_type': alert_type,
                'severity': severity,
                'message': message,
                'triggered_at': datetime.now().isoformat(),
                'acknowledged': False,
                'metrics_snapshot': json.dumps(metrics_snapshot) if metrics_snapshot else None
            }
            
            result = self.supabase.table('alerts').insert(data).execute()
            
            if result.data:
                record_id = result.data[0].get('id')
                logger.info(f"Stored alert: {alert_type} with ID: {record_id}")
                return str(record_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to store alert: {e}")
            return None
    
    # Data Retrieval Methods
    
    async def get_recent_trades(
        self, 
        strategy_name: Optional[str] = None,
        limit: int = 100,
        symbol: Optional[str] = None
    ) -> List[Dict]:
        """
        Get recent trades for analysis.
        
        Args:
            strategy_name: Filter by strategy name
            limit: Maximum number of trades to return
            symbol: Filter by symbol
            
        Returns:
            List of trade records
        """
        try:
            query = self.supabase.table('trades').select('*')
            
            if strategy_name:
                query = query.eq('strategy_name', strategy_name)
            
            if symbol:
                query = query.eq('symbol', symbol)
            
            result = query.order('executed_at', desc=True).limit(limit).execute()
            
            trades = result.data or []
            logger.debug(f"Retrieved {len(trades)} recent trades")
            return trades
            
        except Exception as e:
            logger.error(f"Failed to get recent trades: {e}")
            return []
    
    async def get_portfolio_metrics_history(
        self, 
        hours_back: int = 24,
        limit: int = 1000
    ) -> List[Dict]:
        """
        Get portfolio metrics history.
        
        Args:
            hours_back: Number of hours to look back
            limit: Maximum number of records
            
        Returns:
            List of portfolio metrics records
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            query = self.supabase.table('portfolio_metrics').select('*')
            query = query.gt('timestamp', cutoff_time.isoformat())
            
            result = query.order('timestamp', desc=True).limit(limit).execute()
            
            metrics = result.data or []
            logger.debug(f"Retrieved {len(metrics)} portfolio metrics records")
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get portfolio metrics history: {e}")
            return []
    
    async def get_strategy_performance_comparison(self, days_back: int = 7) -> Dict[str, Dict]:
        """
        Get strategy performance comparison.
        
        Args:
            days_back: Number of days to analyze
            
        Returns:
            Dictionary of strategy performance metrics
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days_back)
            
            query = self.supabase.table('strategy_performance').select('*')
            query = query.gt('timestamp', cutoff_time.isoformat())
            
            result = query.execute()
            
            performance_data = result.data or []
            
            # Aggregate by strategy
            strategy_metrics = {}
            for record in performance_data:
                strategy = record['strategy_name']
                if strategy not in strategy_metrics:
                    strategy_metrics[strategy] = {
                        'total_pnl': 0,
                        'total_trades': 0,
                        'win_rate_sum': 0,
                        'confidence_sum': 0,
                        'count': 0
                    }
                
                metrics = strategy_metrics[strategy]
                metrics['total_pnl'] += record.get('pnl', 0)
                metrics['total_trades'] += record.get('trades_count', 0)
                metrics['win_rate_sum'] += record.get('win_rate', 0)
                metrics['confidence_sum'] += record.get('confidence_score', 0)
                metrics['count'] += 1
            
            # Calculate averages
            for strategy, metrics in strategy_metrics.items():
                if metrics['count'] > 0:
                    metrics['avg_win_rate'] = metrics['win_rate_sum'] / metrics['count']
                    metrics['avg_confidence'] = metrics['confidence_sum'] / metrics['count']
                else:
                    metrics['avg_win_rate'] = 0
                    metrics['avg_confidence'] = 0
            
            logger.debug(f"Generated performance comparison for {len(strategy_metrics)} strategies")
            return strategy_metrics
            
        except Exception as e:
            logger.error(f"Failed to get strategy performance comparison: {e}")
            return {}
    
    async def get_unacknowledged_alerts(self, severity: Optional[str] = None) -> List[Dict]:
        """
        Get unacknowledged alerts.
        
        Args:
            severity: Filter by severity level
            
        Returns:
            List of alert records
        """
        try:
            query = self.supabase.table('alerts').select('*')
            query = query.eq('acknowledged', False)
            
            if severity:
                query = query.eq('severity', severity)
            
            result = query.order('triggered_at', desc=True).execute()
            
            alerts = result.data or []
            logger.info(f"Retrieved {len(alerts)} unacknowledged alerts")
            return alerts
            
        except Exception as e:
            logger.error(f"Failed to get unacknowledged alerts: {e}")
            return []
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """
        Mark an alert as acknowledged.
        
        Args:
            alert_id: ID of the alert to acknowledge
            
        Returns:
            True if successful, False otherwise
        """
        try:
            data = {
                'acknowledged': True,
                'acknowledged_at': datetime.now().isoformat()
            }
            
            result = self.supabase.table('alerts').update(data).eq('id', alert_id).execute()
            
            if result.data:
                logger.info(f"Acknowledged alert {alert_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to acknowledge alert {alert_id}: {e}")
            return False
    
    # Real-time Subscriptions
    
    async def setup_realtime_subscription(self, table_name: str, callback_function: Callable):
        """
        Set up real-time subscriptions for dashboard updates.
        
        Args:
            table_name: Name of the table to subscribe to
            callback_function: Function to call when data changes
        """
        try:
            if SUPABASE_AVAILABLE and hasattr(self.supabase, 'channel'):
                channel = self.supabase.channel(f'realtime:{table_name}')
                channel.on('postgres_changes', callback_function)
                
                self.real_time_subscribers[table_name] = channel
                logger.info(f"Set up real-time subscription for {table_name}")
            else:
                logger.warning(f"Real-time subscriptions not available for {table_name}")
                
        except Exception as e:
            logger.error(f"Failed to set up real-time subscription for {table_name}: {e}")
    
    # Utility Methods
    
    async def test_connection(self) -> bool:
        """
        Test Supabase connection.
        
        Returns:
            True if connection is working, False otherwise
        """
        try:
            if hasattr(self, 'using_real_supabase') and self.using_real_supabase:
                # Test real Supabase connection
                result = self.supabase.table('portfolio_metrics').select('id').limit(1).execute()
                logger.info("✅ Real Supabase connection test successful")
            else:
                # Mock connection always works
                logger.info("✅ Mock Supabase connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Supabase connection test failed: {e}")
            return False
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dictionary with database statistics
        """
        try:
            stats = {}
            
            # Count records in each table
            tables = ['portfolio_metrics', 'trades', 'strategy_performance', 'strategy_weights', 'alerts']
            
            for table in tables:
                try:
                    result = self.supabase.table(table).select('id').execute()
                    stats[f'{table}_count'] = len(result.data) if result.data else 0
                except:
                    stats[f'{table}_count'] = 0
            
            # Get latest portfolio metrics
            try:
                result = self.supabase.table('portfolio_metrics').select('*').order('timestamp', desc=True).limit(1).execute()
                if result.data:
                    stats['latest_portfolio_metrics'] = result.data[0]
            except:
                stats['latest_portfolio_metrics'] = None
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}
    
    async def cleanup_old_data(self, days_to_keep: int = 30) -> Dict[str, int]:
        """
        Clean up old data to manage storage.
        
        Args:
            days_to_keep: Number of days of data to keep
            
        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cleanup_stats = {}
            
            # Tables to clean up (keeping alerts and strategy_weights longer)
            tables_to_clean = ['portfolio_metrics', 'trades', 'strategy_performance']
            
            for table in tables_to_clean:
                try:
                    # For mock client, this would need special handling
                    if isinstance(self.supabase, MockSupabaseClient):
                        # Mock cleanup - just count what would be deleted
                        data = self.supabase.data_store.get(table, [])
                        old_records = [r for r in data if r.get('timestamp', '') < cutoff_date.isoformat()]
                        cleanup_stats[f'{table}_deleted'] = len(old_records)
                    else:
                        # Real Supabase cleanup
                        result = self.supabase.table(table).delete().lt('timestamp', cutoff_date.isoformat()).execute()
                        cleanup_stats[f'{table}_deleted'] = len(result.data) if result.data else 0
                        
                except Exception as table_error:
                    logger.error(f"Failed to cleanup {table}: {table_error}")
                    cleanup_stats[f'{table}_deleted'] = 0
            
            logger.info(f"Cleaned up old data: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            return {}


# Database Schema Creation (for reference)
SUPABASE_SCHEMA_SQL = """
-- Portfolio performance tracking
CREATE TABLE IF NOT EXISTS portfolio_metrics (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_pnl DECIMAL(15, 6) NOT NULL,
    sharpe_ratio DECIMAL(8, 4),
    max_drawdown DECIMAL(8, 4),
    win_rate DECIMAL(8, 4),
    strategy_contributions JSONB,
    correlation_matrix JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy performance tracking
CREATE TABLE IF NOT EXISTS strategy_performance (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    pnl DECIMAL(15, 6) NOT NULL,
    return_pct DECIMAL(8, 6),
    trades_count INTEGER DEFAULT 0,
    win_rate DECIMAL(8, 4),
    confidence_score DECIMAL(8, 4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trade execution tracking
CREATE TABLE IF NOT EXISTS trades (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL, -- BUY, SELL
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(15, 6) NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    pnl DECIMAL(15, 6),
    return_pct DECIMAL(8, 6),
    fees DECIMAL(15, 6),
    confidence DECIMAL(8, 4),
    weight_used DECIMAL(8, 4),
    position_size DECIMAL(8, 4),
    market_conditions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time alerts log
CREATE TABLE IF NOT EXISTS alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    metrics_snapshot JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy weights history
CREATE TABLE IF NOT EXISTS strategy_weights (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    grid_weight DECIMAL(8, 4),
    technical_analysis_weight DECIMAL(8, 4),
    trend_following_weight DECIMAL(8, 4),
    model_confidence DECIMAL(8, 4),
    market_regime VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_timestamp ON portfolio_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_timestamp ON strategy_performance(strategy_name, timestamp);
CREATE INDEX IF NOT EXISTS idx_trades_strategy_timestamp ON trades(strategy_name, executed_at);
CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, executed_at);
CREATE INDEX IF NOT EXISTS idx_alerts_type_timestamp ON alerts(alert_type, triggered_at);
CREATE INDEX IF NOT EXISTS idx_strategy_weights_timestamp ON strategy_weights(timestamp);
"""