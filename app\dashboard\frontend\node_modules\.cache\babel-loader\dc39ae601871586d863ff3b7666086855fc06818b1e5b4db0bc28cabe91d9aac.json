{"ast": null, "code": "/**\n * WebSocket service for real-time analytics updates.\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types for analytics\nexport let AnalyticsEventType = /*#__PURE__*/function (AnalyticsEventType) {\n  AnalyticsEventType[\"MARKET_STATE\"] = \"market_state\";\n  AnalyticsEventType[\"STRATEGY_WEIGHTS\"] = \"strategy_weights\";\n  AnalyticsEventType[\"PERFORMANCE_METRICS\"] = \"performance_metrics\";\n  AnalyticsEventType[\"ERROR\"] = \"error\";\n  AnalyticsEventType[\"CONNECTION_STATUS\"] = \"connection_status\";\n  return AnalyticsEventType;\n}({});\n\n// Define event data interfaces\n\nclass AnalyticsWebSocketService {\n  // 1 second\n\n  constructor() {\n    this.ws = null;\n    this.eventHandlers = {};\n    this.reconnectTimer = null;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 10;\n    this.reconnectDelay = 1000;\n    this.initializeEventHandlers();\n  }\n  initializeEventHandlers() {\n    Object.values(AnalyticsEventType).forEach(type => {\n      this.eventHandlers[type] = [];\n    });\n  }\n  connect() {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      return this.ws;\n    }\n    const analyticsWsUrl = `${WS_URL.replace('http', 'ws')}/analytics`;\n    this.ws = new WebSocket(analyticsWsUrl);\n    this.ws.onopen = this.handleOpen.bind(this);\n    this.ws.onmessage = this.handleMessage.bind(this);\n    this.ws.onclose = this.handleClose.bind(this);\n    this.ws.onerror = this.handleError.bind(this);\n    return this.ws;\n  }\n  disconnect() {\n    if (this.reconnectTimer) {\n      window.clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n    if (this.ws) {\n      this.ws.close(1000, 'User disconnected');\n    }\n  }\n  addEventListener(eventType, handler) {\n    if (this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType].push(handler);\n    }\n  }\n  removeEventListener(eventType, handler) {\n    if (this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType] = this.eventHandlers[eventType].filter(h => h !== handler);\n    }\n  }\n  handleOpen() {\n    console.log('Analytics WebSocket connected');\n    this.reconnectAttempts = 0;\n    const statusEvent = {\n      status: 'connected',\n      message: 'Analytics WebSocket connected'\n    };\n    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);\n  }\n  handleMessage(event) {\n    try {\n      const message = JSON.parse(event.data);\n      if (message.type && this.eventHandlers[message.type]) {\n        this.notifyHandlers(message.type, message.data);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error);\n    }\n  }\n  handleClose(event) {\n    console.log(`Analytics WebSocket closed: ${event.code} - ${event.reason}`);\n    const statusEvent = {\n      status: 'disconnected',\n      message: `Analytics WebSocket closed: ${event.reason || 'Normal closure'}`\n    };\n    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n  handleError(event) {\n    console.error('Analytics WebSocket error:', event);\n    const errorEvent = {\n      code: 'connection_error',\n      message: 'Analytics WebSocket connection error'\n    };\n    this.notifyHandlers(AnalyticsEventType.ERROR, errorEvent);\n  }\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Max reconnection attempts for analytics reached');\n      return;\n    }\n    if (this.reconnectTimer) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n  notifyHandlers(eventType, data) {\n    const handlers = this.eventHandlers[eventType];\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error(`Error in ${eventType} handler:`, error);\n        }\n      });\n    }\n  }\n}\nexport const analyticsWebsocketService = new AnalyticsWebSocketService();\nexport default analyticsWebsocketService;", "map": {"version": 3, "names": ["WS_URL", "AnalyticsEventType", "AnalyticsWebSocketService", "constructor", "ws", "eventHandlers", "reconnectTimer", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "initializeEventHandlers", "Object", "values", "for<PERSON>ach", "type", "connect", "readyState", "WebSocket", "OPEN", "analyticsWsUrl", "replace", "onopen", "handleOpen", "bind", "onmessage", "handleMessage", "onclose", "handleClose", "onerror", "handleError", "disconnect", "window", "clearTimeout", "close", "addEventListener", "eventType", "handler", "push", "removeEventListener", "filter", "h", "console", "log", "statusEvent", "status", "message", "notifyHandlers", "CONNECTION_STATUS", "event", "JSON", "parse", "data", "error", "code", "reason", "scheduleReconnect", "errorEvent", "ERROR", "delay", "Math", "pow", "setTimeout", "handlers", "analyticsWebsocketService"], "sources": ["C:/Users/<USER>/Documents/Programming/Crypto_App_V2/app/dashboard/frontend/src/services/analyticsWebsocket.ts"], "sourcesContent": ["/**\n * WebSocket service for real-time analytics updates.\n */\n\nimport { WS_URL } from '../config';\n\n// Define event types for analytics\nexport enum AnalyticsEventType {\n  MARKET_STATE = 'market_state',\n  STRATEGY_WEIGHTS = 'strategy_weights',\n  PERFORMANCE_METRICS = 'performance_metrics',\n  ERROR = 'error',\n  CONNECTION_STATUS = 'connection_status',\n}\n\n// Define event data interfaces\nexport interface MarketStateEvent {\n  timestamp: string;\n  regime: string;\n  volatility: number;\n  trend_strength: number;\n}\n\nexport interface StrategyWeightsEvent {\n  timestamp: string;\n  weights: Record<string, number>;\n}\n\nexport interface PerformanceMetricsEvent {\n  timestamp: string;\n  metrics: Record<string, any>;\n}\n\nexport interface ConnectionStatusEvent {\n    status: 'connected' | 'disconnected' | 'connecting';\n    message: string;\n}\n\nexport interface ErrorEvent {\n    code: string;\n    message: string;\n}\n\ntype AnalyticsEventHandler = (data: any) => void;\n\nclass AnalyticsWebSocketService {\n  private ws: WebSocket | null = null;\n  private eventHandlers: { [key: string]: AnalyticsEventHandler[] } = {};\n  private reconnectTimer: number | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 10;\n  private reconnectDelay = 1000; // 1 second\n\n  constructor() {\n    this.initializeEventHandlers();\n  }\n\n  private initializeEventHandlers() {\n    Object.values(AnalyticsEventType).forEach(type => {\n      this.eventHandlers[type] = [];\n    });\n  }\n\n  public connect(): WebSocket {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      return this.ws;\n    }\n\n    const analyticsWsUrl = `${WS_URL.replace('http', 'ws')}/analytics`;\n    this.ws = new WebSocket(analyticsWsUrl);\n\n    this.ws.onopen = this.handleOpen.bind(this);\n    this.ws.onmessage = this.handleMessage.bind(this);\n    this.ws.onclose = this.handleClose.bind(this);\n    this.ws.onerror = this.handleError.bind(this);\n\n    return this.ws;\n  }\n\n  public disconnect(): void {\n    if (this.reconnectTimer) {\n        window.clearTimeout(this.reconnectTimer);\n        this.reconnectTimer = null;\n    }\n    if (this.ws) {\n      this.ws.close(1000, 'User disconnected');\n    }\n  }\n\n  public addEventListener(eventType: AnalyticsEventType, handler: AnalyticsEventHandler): void {\n    if (this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType].push(handler);\n    }\n  }\n\n  public removeEventListener(eventType: AnalyticsEventType, handler: AnalyticsEventHandler): void {\n    if (this.eventHandlers[eventType]) {\n      this.eventHandlers[eventType] = this.eventHandlers[eventType].filter(h => h !== handler);\n    }\n  }\n\n  private handleOpen(): void {\n    console.log('Analytics WebSocket connected');\n    this.reconnectAttempts = 0;\n    const statusEvent: ConnectionStatusEvent = {\n        status: 'connected',\n        message: 'Analytics WebSocket connected',\n    };\n    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);\n  }\n\n  private handleMessage(event: MessageEvent): void {\n    try {\n      const message = JSON.parse(event.data);\n      if (message.type && this.eventHandlers[message.type]) {\n        this.notifyHandlers(message.type, message.data);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error);\n    }\n  }\n\n  private handleClose(event: CloseEvent): void {\n    console.log(`Analytics WebSocket closed: ${event.code} - ${event.reason}`);\n    const statusEvent: ConnectionStatusEvent = {\n        status: 'disconnected',\n        message: `Analytics WebSocket closed: ${event.reason || 'Normal closure'}`,\n    };\n    this.notifyHandlers(AnalyticsEventType.CONNECTION_STATUS, statusEvent);\n\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  private handleError(event: Event): void {\n    console.error('Analytics WebSocket error:', event);\n    const errorEvent: ErrorEvent = {\n        code: 'connection_error',\n        message: 'Analytics WebSocket connection error',\n    };\n    this.notifyHandlers(AnalyticsEventType.ERROR, errorEvent);\n  }\n\n  private scheduleReconnect(): void {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log('Max reconnection attempts for analytics reached');\n      return;\n    }\n\n    if (this.reconnectTimer) {\n      window.clearTimeout(this.reconnectTimer);\n    }\n\n    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);\n    this.reconnectTimer = window.setTimeout(() => {\n      this.reconnectAttempts++;\n      this.connect();\n    }, delay);\n  }\n\n  private notifyHandlers(eventType: AnalyticsEventType, data: any): void {\n    const handlers = this.eventHandlers[eventType];\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error(`Error in ${eventType} handler:`, error);\n        }\n      });\n    }\n  }\n}\n\nexport const analyticsWebsocketService = new AnalyticsWebSocketService();\nexport default analyticsWebsocketService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,MAAM,QAAQ,WAAW;;AAElC;AACA,WAAYC,kBAAkB,0BAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAlBA,kBAAkB;EAAA,OAAlBA,kBAAkB;AAAA;;AAQ9B;;AA8BA,MAAMC,yBAAyB,CAAC;EAMC;;EAE/BC,WAAWA,CAAA,EAAG;IAAA,KAPNC,EAAE,GAAqB,IAAI;IAAA,KAC3BC,aAAa,GAA+C,CAAC,CAAC;IAAA,KAC9DC,cAAc,GAAkB,IAAI;IAAA,KACpCC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,oBAAoB,GAAG,EAAE;IAAA,KACzBC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC;EAEQA,uBAAuBA,CAAA,EAAG;IAChCC,MAAM,CAACC,MAAM,CAACX,kBAAkB,CAAC,CAACY,OAAO,CAACC,IAAI,IAAI;MAChD,IAAI,CAACT,aAAa,CAACS,IAAI,CAAC,GAAG,EAAE;IAC/B,CAAC,CAAC;EACJ;EAEOC,OAAOA,CAAA,EAAc;IAC1B,IAAI,IAAI,CAACX,EAAE,IAAI,IAAI,CAACA,EAAE,CAACY,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MACpD,OAAO,IAAI,CAACd,EAAE;IAChB;IAEA,MAAMe,cAAc,GAAG,GAAGnB,MAAM,CAACoB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY;IAClE,IAAI,CAAChB,EAAE,GAAG,IAAIa,SAAS,CAACE,cAAc,CAAC;IAEvC,IAAI,CAACf,EAAE,CAACiB,MAAM,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC3C,IAAI,CAACnB,EAAE,CAACoB,SAAS,GAAG,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI,CAAC;IACjD,IAAI,CAACnB,EAAE,CAACsB,OAAO,GAAG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC7C,IAAI,CAACnB,EAAE,CAACwB,OAAO,GAAG,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,IAAI,CAAC;IAE7C,OAAO,IAAI,CAACnB,EAAE;EAChB;EAEO0B,UAAUA,CAAA,EAAS;IACxB,IAAI,IAAI,CAACxB,cAAc,EAAE;MACrByB,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1B,cAAc,CAAC;MACxC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACF,EAAE,EAAE;MACX,IAAI,CAACA,EAAE,CAAC6B,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;IAC1C;EACF;EAEOC,gBAAgBA,CAACC,SAA6B,EAAEC,OAA8B,EAAQ;IAC3F,IAAI,IAAI,CAAC/B,aAAa,CAAC8B,SAAS,CAAC,EAAE;MACjC,IAAI,CAAC9B,aAAa,CAAC8B,SAAS,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;IAC7C;EACF;EAEOE,mBAAmBA,CAACH,SAA6B,EAAEC,OAA8B,EAAQ;IAC9F,IAAI,IAAI,CAAC/B,aAAa,CAAC8B,SAAS,CAAC,EAAE;MACjC,IAAI,CAAC9B,aAAa,CAAC8B,SAAS,CAAC,GAAG,IAAI,CAAC9B,aAAa,CAAC8B,SAAS,CAAC,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;IAC1F;EACF;EAEQd,UAAUA,CAAA,EAAS;IACzBmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACnC,iBAAiB,GAAG,CAAC;IAC1B,MAAMoC,WAAkC,GAAG;MACvCC,MAAM,EAAE,WAAW;MACnBC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,cAAc,CAAC7C,kBAAkB,CAAC8C,iBAAiB,EAAEJ,WAAW,CAAC;EACxE;EAEQlB,aAAaA,CAACuB,KAAmB,EAAQ;IAC/C,IAAI;MACF,MAAMH,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;MACtC,IAAIN,OAAO,CAAC/B,IAAI,IAAI,IAAI,CAACT,aAAa,CAACwC,OAAO,CAAC/B,IAAI,CAAC,EAAE;QACpD,IAAI,CAACgC,cAAc,CAACD,OAAO,CAAC/B,IAAI,EAAE+B,OAAO,CAACM,IAAI,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF;EAEQzB,WAAWA,CAACqB,KAAiB,EAAQ;IAC3CP,OAAO,CAACC,GAAG,CAAC,+BAA+BM,KAAK,CAACK,IAAI,MAAML,KAAK,CAACM,MAAM,EAAE,CAAC;IAC1E,MAAMX,WAAkC,GAAG;MACvCC,MAAM,EAAE,cAAc;MACtBC,OAAO,EAAE,+BAA+BG,KAAK,CAACM,MAAM,IAAI,gBAAgB;IAC5E,CAAC;IACD,IAAI,CAACR,cAAc,CAAC7C,kBAAkB,CAAC8C,iBAAiB,EAAEJ,WAAW,CAAC;IAEtE,IAAIK,KAAK,CAACK,IAAI,KAAK,IAAI,EAAE;MACvB,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC1B;EACF;EAEQ1B,WAAWA,CAACmB,KAAY,EAAQ;IACtCP,OAAO,CAACW,KAAK,CAAC,4BAA4B,EAAEJ,KAAK,CAAC;IAClD,MAAMQ,UAAsB,GAAG;MAC3BH,IAAI,EAAE,kBAAkB;MACxBR,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,cAAc,CAAC7C,kBAAkB,CAACwD,KAAK,EAAED,UAAU,CAAC;EAC3D;EAEQD,iBAAiBA,CAAA,EAAS;IAChC,IAAI,IAAI,CAAChD,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDiC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D;IACF;IAEA,IAAI,IAAI,CAACpC,cAAc,EAAE;MACvByB,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1B,cAAc,CAAC;IAC1C;IAEA,MAAMoD,KAAK,GAAG,IAAI,CAACjD,cAAc,GAAGkD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACrD,iBAAiB,CAAC;IACzE,IAAI,CAACD,cAAc,GAAGyB,MAAM,CAAC8B,UAAU,CAAC,MAAM;MAC5C,IAAI,CAACtD,iBAAiB,EAAE;MACxB,IAAI,CAACQ,OAAO,CAAC,CAAC;IAChB,CAAC,EAAE2C,KAAK,CAAC;EACX;EAEQZ,cAAcA,CAACX,SAA6B,EAAEgB,IAAS,EAAQ;IACrE,MAAMW,QAAQ,GAAG,IAAI,CAACzD,aAAa,CAAC8B,SAAS,CAAC;IAC9C,IAAI2B,QAAQ,EAAE;MACZA,QAAQ,CAACjD,OAAO,CAACuB,OAAO,IAAI;QAC1B,IAAI;UACFA,OAAO,CAACe,IAAI,CAAC;QACf,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,YAAYjB,SAAS,WAAW,EAAEiB,KAAK,CAAC;QACxD;MACF,CAAC,CAAC;IACJ;EACF;AACF;AAEA,OAAO,MAAMW,yBAAyB,GAAG,IAAI7D,yBAAyB,CAAC,CAAC;AACxE,eAAe6D,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}